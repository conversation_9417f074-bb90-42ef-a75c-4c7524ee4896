{"digest": "empty", "schema": {"__struct__": "<PERSON><PERSON><PERSON>", "attributes": {"fields": [{"__struct__": "Field", "attributes": {"meta": {"check_constraints": [], "default": null, "embed": false, "foreign_key": false, "index": false, "index_name": null, "nullable": true, "primary_key": true, "type": ["atom", "integer"]}, "name": ["atom", "id"], "source": ["atom", "id"], "type": ["atom", "integer"]}}, {"__struct__": "Field", "attributes": {"meta": {"check_constraints": [], "default": null, "embed": false, "foreign_key": false, "index": true, "index_name": "users_email_index", "nullable": false, "primary_key": false, "type": ["atom", "string"]}, "name": ["atom", "email"], "source": ["atom", "email"], "type": ["atom", "string"]}}, {"__struct__": "Field", "attributes": {"meta": {"check_constraints": [], "default": null, "embed": false, "foreign_key": false, "index": false, "index_name": null, "nullable": true, "primary_key": false, "type": ["atom", "string"]}, "name": ["atom", "name"], "source": ["atom", "name"], "type": ["atom", "string"]}}, {"__struct__": "Field", "attributes": {"meta": {"check_constraints": [], "default": null, "embed": false, "foreign_key": false, "index": false, "index_name": null, "nullable": true, "primary_key": false, "type": ["atom", "integer"]}, "name": ["atom", "age"], "source": ["atom", "age"], "type": ["atom", "integer"]}}, {"__struct__": "Field", "attributes": {"meta": {"check_constraints": [], "default": true, "embed": false, "foreign_key": false, "index": true, "index_name": "users_active_index", "nullable": true, "primary_key": false, "type": ["atom", "integer"]}, "name": ["atom", "active"], "source": ["atom", "active"], "type": ["atom", "boolean"]}}, {"__struct__": "Field", "attributes": {"meta": {"check_constraints": [], "default": null, "embed": false, "foreign_key": false, "index": false, "index_name": null, "nullable": true, "primary_key": false, "type": ["atom", "string"]}, "name": ["atom", "profile_data"], "source": ["atom", "profile_data"], "type": ["atom", "string"]}}, {"__struct__": "Field", "attributes": {"meta": {"check_constraints": [], "default": [], "embed": false, "foreign_key": false, "index": false, "index_name": null, "nullable": true, "primary_key": false, "type": ["atom", "string"]}, "name": ["atom", "tags"], "source": ["atom", "tags"], "type": ["tuple", [["atom", "array"], ["atom", "any"]]]}}, {"__struct__": "Field", "attributes": {"meta": {"check_constraints": [], "default": null, "embed": false, "foreign_key": false, "index": false, "index_name": null, "nullable": true, "primary_key": false, "type": ["atom", "decimal"]}, "name": ["atom", "score"], "source": ["atom", "score"], "type": ["atom", "decimal"]}}, {"__struct__": "Field", "attributes": {"meta": {"check_constraints": [], "default": null, "embed": false, "foreign_key": false, "index": false, "index_name": null, "nullable": true, "primary_key": false, "type": ["atom", "string"]}, "name": ["atom", "birth_date"], "source": ["atom", "birth_date"], "type": ["atom", "string"]}}, {"__struct__": "Field", "attributes": {"meta": {"check_constraints": [], "default": null, "embed": false, "foreign_key": false, "index": false, "index_name": null, "nullable": true, "primary_key": false, "type": ["atom", "string"]}, "name": ["atom", "last_login_at"], "source": ["atom", "last_login_at"], "type": ["atom", "string"]}}, {"__struct__": "Field", "attributes": {"meta": {"check_constraints": [], "default": null, "embed": false, "foreign_key": false, "index": false, "index_name": null, "nullable": false, "primary_key": false, "type": ["atom", "string"]}, "name": ["atom", "inserted_at"], "source": ["atom", "inserted_at"], "type": ["atom", "string"]}}, {"__struct__": "Field", "attributes": {"meta": {"check_constraints": [], "default": null, "embed": false, "foreign_key": false, "index": false, "index_name": null, "nullable": false, "primary_key": false, "type": ["atom", "string"]}, "name": ["atom", "updated_at"], "source": ["atom", "updated_at"], "type": ["atom", "string"]}}], "foreign_keys": [], "indices": [{"__struct__": "Index", "attributes": {"composite": false, "fields": [{"__struct__": "Field", "attributes": {"meta": {"check_constraints": [], "default": true, "embed": false, "foreign_key": false, "index": true, "index_name": "users_active_index", "nullable": true, "primary_key": false, "type": ["atom", "integer"]}, "name": ["atom", "active"], "source": ["atom", "active"], "type": ["atom", "boolean"]}}], "name": ["atom", "users_active_index"], "type": ["atom", "btree"], "unique": false}}, {"__struct__": "Index", "attributes": {"composite": false, "fields": [{"__struct__": "Field", "attributes": {"meta": {"check_constraints": [], "default": null, "embed": false, "foreign_key": false, "index": true, "index_name": "users_email_index", "nullable": false, "primary_key": false, "type": ["atom", "string"]}, "name": ["atom", "email"], "source": ["atom", "email"], "type": ["atom", "string"]}}], "name": ["atom", "users_email_index"], "type": ["atom", "btree"], "unique": true}}], "primary_key": {"__struct__": "<PERSON><PERSON><PERSON>", "attributes": {"fields": [{"__struct__": "Field", "attributes": {"meta": {"check_constraints": [], "default": null, "embed": false, "foreign_key": false, "index": false, "index_name": null, "nullable": true, "primary_key": true, "type": ["atom", "integer"]}, "name": ["atom", "id"], "source": ["atom", "id"], "type": ["atom", "integer"]}}], "meta": {"composite": false}}}, "source": ["atom", "users"]}}}