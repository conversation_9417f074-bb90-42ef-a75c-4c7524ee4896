{"digest": "empty", "schema": {"__struct__": "<PERSON><PERSON><PERSON>", "attributes": {"fields": [{"__struct__": "Field", "attributes": {"meta": {"check_constraints": [], "default": null, "embed": false, "foreign_key": false, "index": false, "index_name": null, "nullable": true, "primary_key": true, "type": ["atom", "integer"]}, "name": ["atom", "id"], "source": ["atom", "id"], "type": ["atom", "integer"]}}, {"__struct__": "Field", "attributes": {"meta": {"check_constraints": [], "default": null, "embed": false, "foreign_key": false, "index": false, "index_name": null, "nullable": false, "primary_key": false, "type": ["atom", "string"]}, "name": ["atom", "body"], "source": ["atom", "body"], "type": ["atom", "string"]}}, {"__struct__": "Field", "attributes": {"meta": {"check_constraints": [], "default": false, "embed": false, "foreign_key": false, "index": true, "index_name": "comments_approved_index", "nullable": true, "primary_key": false, "type": ["atom", "integer"]}, "name": ["atom", "approved"], "source": ["atom", "approved"], "type": ["atom", "boolean"]}}, {"__struct__": "Field", "attributes": {"meta": {"check_constraints": [], "default": null, "embed": false, "foreign_key": true, "index": true, "index_name": "comments_user_id_index", "nullable": false, "primary_key": false, "type": ["atom", "integer"]}, "name": ["atom", "user_id"], "source": ["atom", "user_id"], "type": ["atom", "integer"]}}, {"__struct__": "Field", "attributes": {"meta": {"check_constraints": [], "default": null, "embed": false, "foreign_key": true, "index": true, "index_name": "comments_post_id_index", "nullable": false, "primary_key": false, "type": ["atom", "integer"]}, "name": ["atom", "post_id"], "source": ["atom", "post_id"], "type": ["atom", "integer"]}}, {"__struct__": "Field", "attributes": {"meta": {"check_constraints": [], "default": null, "embed": false, "foreign_key": false, "index": false, "index_name": null, "nullable": false, "primary_key": false, "type": ["atom", "string"]}, "name": ["atom", "inserted_at"], "source": ["atom", "inserted_at"], "type": ["atom", "string"]}}, {"__struct__": "Field", "attributes": {"meta": {"check_constraints": [], "default": null, "embed": false, "foreign_key": false, "index": false, "index_name": null, "nullable": false, "primary_key": false, "type": ["atom", "string"]}, "name": ["atom", "updated_at"], "source": ["atom", "updated_at"], "type": ["atom", "string"]}}], "foreign_keys": [{"__struct__": "ForeignKey", "attributes": {"field": ["atom", "post_id"], "references_field": ["atom", "id"], "references_table": ["atom", "posts"]}}, {"__struct__": "ForeignKey", "attributes": {"field": ["atom", "user_id"], "references_field": ["atom", "id"], "references_table": ["atom", "users"]}}], "indices": [{"__struct__": "Index", "attributes": {"composite": false, "fields": [{"__struct__": "Field", "attributes": {"meta": {"check_constraints": [], "default": false, "embed": false, "foreign_key": false, "index": true, "index_name": "comments_approved_index", "nullable": true, "primary_key": false, "type": ["atom", "integer"]}, "name": ["atom", "approved"], "source": ["atom", "approved"], "type": ["atom", "boolean"]}}], "name": ["atom", "comments_approved_index"], "type": ["atom", "btree"], "unique": false}}, {"__struct__": "Index", "attributes": {"composite": false, "fields": [{"__struct__": "Field", "attributes": {"meta": {"check_constraints": [], "default": null, "embed": false, "foreign_key": true, "index": true, "index_name": "comments_post_id_index", "nullable": false, "primary_key": false, "type": ["atom", "integer"]}, "name": ["atom", "post_id"], "source": ["atom", "post_id"], "type": ["atom", "integer"]}}], "name": ["atom", "comments_post_id_index"], "type": ["atom", "btree"], "unique": false}}, {"__struct__": "Index", "attributes": {"composite": false, "fields": [{"__struct__": "Field", "attributes": {"meta": {"check_constraints": [], "default": null, "embed": false, "foreign_key": true, "index": true, "index_name": "comments_user_id_index", "nullable": false, "primary_key": false, "type": ["atom", "integer"]}, "name": ["atom", "user_id"], "source": ["atom", "user_id"], "type": ["atom", "integer"]}}], "name": ["atom", "comments_user_id_index"], "type": ["atom", "btree"], "unique": false}}], "primary_key": {"__struct__": "<PERSON><PERSON><PERSON>", "attributes": {"fields": [{"__struct__": "Field", "attributes": {"meta": {"check_constraints": [], "default": null, "embed": false, "foreign_key": false, "index": false, "index_name": null, "nullable": true, "primary_key": true, "type": ["atom", "integer"]}, "name": ["atom", "id"], "source": ["atom", "id"], "type": ["atom", "integer"]}}], "meta": {"composite": false}}}, "source": ["atom", "comments"]}}}